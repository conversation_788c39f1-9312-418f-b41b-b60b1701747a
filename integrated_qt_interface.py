import sys
import json
import threading
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QTextEdit, QGroupBox, QGridLayout, QSlider, QSpinBox,
                             QComboBox, QDoubleSpinBox, QSplitter, QLineEdit)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QTextCursor

def print_colored(text, color='green'):
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['green'])}{text}{colors['end']}")

from audio_recognition import AudioRecognizer
from qwen_decision import QwenDecisionMaker
from ros_bridge import ROSBridgePublisher
from movement_controller import MovementController

class IntegratedRobotControlUI(QMainWindow):
    """集成机器人控制Qt界面"""
    
    # 自定义信号
    update_status_signal = pyqtSignal(str, str)  # 参数：消息内容，颜色
    update_recognition_signal = pyqtSignal(str)
    update_decision_signal = pyqtSignal(str)
    update_thinking_signal = pyqtSignal(str)  # 用于更新思考过程
    update_ros_signal = pyqtSignal(str, str)  # 参数：消息内容，颜色
    start_stop_timer_signal = pyqtSignal(int) # 参数：停止计时器延迟（毫秒）
    
    def __init__(self, ros_bridge_url="ws://192.168.0.109:9090"):
        super().__init__()
        
        # 初始化模块
        print_colored("初始化语音识别模块...", "blue")
        self.recognizer = AudioRecognizer()
        
        print_colored("初始化决策分析模块...", "blue")
        self.decision_maker = QwenDecisionMaker()
        
        print_colored(f"初始化ROS桥接模块 (URL: {ros_bridge_url})...", "blue")
        self.ros_publisher = ROSBridgePublisher(ros_bridge_url=ros_bridge_url)
        self.movement_controller = self.ros_publisher.movement_controller
        
        # 连接状态
        self.ros_connected = False
        
        # 录音时长
        self.record_duration = 3
        
        # 速度和距离设置
        self.current_speed = 0.5  # 默认速度0.5m/s
        self.current_distance = 1.0  # 默认距离1米
        
        # 初始化UI
        self.init_ui()
        
        # 连接信号到槽
        self.update_status_signal.connect(self.update_status)
        self.update_recognition_signal.connect(self.update_recognition_text)
        self.update_decision_signal.connect(self.update_decision_text)
        self.update_thinking_signal.connect(self.update_thinking_text)
        self.update_ros_signal.connect(self.update_ros_status)
        self.start_stop_timer_signal.connect(self._start_stop_timer_slot)

        # 尝试连接到ROS
        self.connect_to_ros()

    def _send_stop_command(self):
        """发送停止命令 (零速度)"""
        if self.ros_connected:
            self.update_ros_signal.emit("正在发送停止命令...", "blue")
            self.ros_publisher.publish_cmd_vel() # 发送零速度
            self.update_ros_signal.emit("停止命令已发送", "green")
        else:
            self.update_ros_signal.emit("ROS未连接，无法发送停止命令", "red")
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle('集成机器人控制系统')
        self.setGeometry(100, 100, 1000, 700)

        # 应用样式表
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #e0f2f7, stop:1 #b3e5fc);
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
                background-color: #e0e0e0;
                border-radius: 3px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                text-align: center;
                text-decoration: none;
                font-size: 16px;
                margin: 4px 2px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3e8e41;
            }
            QPushButton#record_button {
                background-color: #f44336;
            }
            QPushButton#record_button:hover {
                background-color: #da190b;
            }
            QPushButton#record_button:pressed {
                background-color: #c21807;
            }
            QPushButton#connect_button {
                background-color: #2196F3;
            }
            QPushButton#connect_button:hover {
                background-color: #0b7dda;
            }
            QPushButton#connect_button:pressed {
                background-color: #0a6ebd;
            }
            QTextEdit {
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f8f8f8;
            }
            QLabel {
                font-size: 15px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bbb;
                background: white;
                height: 8px;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #4CAF50;
                border: 1px solid #4CAF50;
                width: 18px;
                margin: -2px 0; 
                border-radius: 9px;
            }
            QSpinBox, QDoubleSpinBox, QComboBox, QLineEdit {
                padding: 3px;
                border: 1px solid #cccccc;
                border-radius: 3px;
            }
            QComboBox::drop-down {
                border: none;
            }
        """)
    
        # 创建中央部件和主布局
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        
        # 状态栏
        self.status_label = QLabel('准备就绪')
        self.status_label.setStyleSheet('color: green; font-weight: bold;')
        main_layout.addWidget(self.status_label)
        
        # 创建上部布局（语音识别和决策分析）
        top_layout = QHBoxLayout()
        top_splitter = QSplitter(Qt.Horizontal)
        
        # 语音识别部分
        recognition_group = QGroupBox('语音识别')
        recognition_layout = QVBoxLayout(recognition_group)
        
        # 录音按钮和时长设置
        record_layout = QHBoxLayout()
        self.record_button = QPushButton('开始对话')
        self.record_button.setObjectName('record_button')
        self.record_button.clicked.connect(self.start_recognition)
        record_layout.addWidget(self.record_button)
        
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel('录音时长:'))
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 10)
        self.duration_spin.setValue(self.record_duration)
        self.duration_spin.valueChanged.connect(self.update_duration)
        duration_layout.addWidget(self.duration_spin)
        duration_layout.addWidget(QLabel('秒'))
        record_layout.addLayout(duration_layout)
        
        recognition_layout.addLayout(record_layout)
        
        # 识别结果显示
        recognition_layout.addWidget(QLabel('识别结果:'))
        self.recognition_text = QTextEdit()
        self.recognition_text.setReadOnly(True)
        recognition_layout.addWidget(self.recognition_text)
        
        # 决策分析部分
        decision_group = QGroupBox('决策分析')
        decision_layout = QVBoxLayout(decision_group)
        
        # 添加思考过程显示区域
        decision_layout.addWidget(QLabel('思考过程:'))
        self.thinking_text = QTextEdit()
        self.thinking_text.setReadOnly(True)
        decision_layout.addWidget(self.thinking_text)
        
        # 决策结果
        decision_layout.addWidget(QLabel('决策结果:'))
        self.decision_text = QTextEdit()
        self.decision_text.setReadOnly(True)
        decision_layout.addWidget(self.decision_text)
        
        # 将语音识别和决策分析添加到上部布局
        top_splitter.addWidget(recognition_group)
        top_splitter.addWidget(decision_group)
        top_splitter.setSizes([300, 300])  # 均分宽度
        top_layout.addWidget(top_splitter)
        main_layout.addLayout(top_layout)
        
        # 创建中部布局（速度和距离控制）
        middle_layout = QHBoxLayout()
        
        # 速度控制部分
        speed_group = QGroupBox('速度控制')
        speed_layout = QVBoxLayout(speed_group)
        
        # 线速度控制
        linear_layout = QHBoxLayout()
        linear_layout.addWidget(QLabel('线速度:'))
        self.linear_speed_slider = QSlider(Qt.Horizontal)
        self.linear_speed_slider.setRange(0, 100)
        self.linear_speed_slider.setValue(int(self.current_speed * 100))
        self.linear_speed_slider.valueChanged.connect(self.update_linear_speed)
        linear_layout.addWidget(self.linear_speed_slider)
        self.linear_speed_label = QLabel(f'{self.current_speed:.2f} m/s')
        linear_layout.addWidget(self.linear_speed_label)
        speed_layout.addLayout(linear_layout)
        
        # 角速度控制
        angular_layout = QHBoxLayout()
        angular_layout.addWidget(QLabel('角速度:'))
        self.angular_speed_slider = QSlider(Qt.Horizontal)
        self.angular_speed_slider.setRange(0, 100)
        self.angular_speed_slider.setValue(50)
        self.angular_speed_slider.valueChanged.connect(self.update_angular_speed)
        angular_layout.addWidget(self.angular_speed_slider)
        self.angular_speed_label = QLabel('0.50 rad/s')
        angular_layout.addWidget(self.angular_speed_label)
        speed_layout.addLayout(angular_layout)
        
        # 预设速度组合
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel('预设:'))
        self.preset_combo = QComboBox()
        self.preset_combo.addItems(['慢速', '中速', '快速'])
        self.preset_combo.currentIndexChanged.connect(self.select_preset_speed)
        preset_layout.addWidget(self.preset_combo)
        speed_layout.addLayout(preset_layout)
        
        middle_layout.addWidget(speed_group)
        
        # 距离控制部分
        distance_group = QGroupBox('距离控制')
        distance_layout = QVBoxLayout(distance_group)
        
        # 距离设置
        distance_slider_layout = QHBoxLayout()
        distance_slider_layout.addWidget(QLabel('距离:'))
        self.distance_slider = QSlider(Qt.Horizontal)
        self.distance_slider.setRange(0, 500)
        self.distance_slider.setValue(int(self.current_distance * 100))
        self.distance_slider.valueChanged.connect(self.update_distance)
        distance_slider_layout.addWidget(self.distance_slider)
        self.distance_label = QLabel(f'{self.current_distance:.2f} m')
        distance_slider_layout.addWidget(self.distance_label)
        distance_layout.addLayout(distance_slider_layout)
        
        # 精确距离和执行按钮
        precise_layout = QHBoxLayout()
        precise_layout.addWidget(QLabel('精确设置:'))
        self.distance_spin = QDoubleSpinBox()
        self.distance_spin.setRange(0.1, 5.0)
        self.distance_spin.setSingleStep(0.1)
        self.distance_spin.setValue(self.current_distance)
        self.distance_spin.valueChanged.connect(lambda v: self.distance_slider.setValue(int(v * 100)))
        precise_layout.addWidget(self.distance_spin)
        precise_layout.addWidget(QLabel('m'))
        
        self.move_distance_btn = QPushButton('按距离移动')
        self.move_distance_btn.clicked.connect(self.move_with_distance)
        precise_layout.addWidget(self.move_distance_btn)
        distance_layout.addLayout(precise_layout)
        
        middle_layout.addWidget(distance_group)
        main_layout.addLayout(middle_layout)
        
        # 创建下部布局（ROS连接和手动控制）
        bottom_layout = QHBoxLayout()
        
        # ROS连接状态
        ros_group = QGroupBox('ROS连接')
        ros_layout = QVBoxLayout(ros_group)
        
        # 连接按钮和状态
        ros_conn_layout = QHBoxLayout()
        self.connect_button = QPushButton('连接ROS')
        self.connect_button.setObjectName('connect_button')
        self.connect_button.clicked.connect(self.connect_to_ros)
        ros_conn_layout.addWidget(self.connect_button)
        
        self.ros_status = QLabel('未连接')
        self.ros_status.setStyleSheet('color: red; font-weight: bold;')
        ros_conn_layout.addWidget(self.ros_status)
        
        ros_layout.addLayout(ros_conn_layout)
        
        # 添加ROSBridge URL设置
        ros_url_layout = QHBoxLayout()
        ros_url_layout.addWidget(QLabel('ROSBridge URL:'))
        self.ros_url_input = QLineEdit(self.ros_publisher.ros_bridge_url)
        ros_url_layout.addWidget(self.ros_url_input)
        self.apply_url_btn = QPushButton('连接')
        self.apply_url_btn.clicked.connect(self.update_ros_bridge_url)
        ros_url_layout.addWidget(self.apply_url_btn)
        ros_layout.addLayout(ros_url_layout)
        
        # ROS命令日志
        ros_layout.addWidget(QLabel('ROS命令日志:'))
        self.ros_log = QTextEdit()
        self.ros_log.setReadOnly(True)
        ros_layout.addWidget(self.ros_log)
        
        bottom_layout.addWidget(ros_group)
        
        # 手动控制面板
        control_group = QGroupBox('手动控制')
        control_layout = QGridLayout(control_group)
        
        # 方向按钮
        self.forward_btn = QPushButton('前进')
        self.forward_btn.pressed.connect(lambda: self.manual_control('前进'))
        self.forward_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.backward_btn = QPushButton('后退')
        self.backward_btn.pressed.connect(lambda: self.manual_control('后退'))
        self.backward_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.left_btn = QPushButton('左转')
        self.left_btn.pressed.connect(lambda: self.manual_control('左转'))
        self.left_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.right_btn = QPushButton('右转')
        self.right_btn.pressed.connect(lambda: self.manual_control('右转'))
        self.right_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.stop_btn = QPushButton('停止')
        self.stop_btn.clicked.connect(lambda: self.manual_control('停止'))
        
        # 放置按钮
        control_layout.addWidget(self.forward_btn, 0, 1)
        control_layout.addWidget(self.left_btn, 1, 0)
        control_layout.addWidget(self.stop_btn, 1, 1)
        control_layout.addWidget(self.right_btn, 1, 2)
        control_layout.addWidget(self.backward_btn, 2, 1)
        
        # 添加自定义命令区域
        custom_layout = QVBoxLayout()
        custom_layout.addWidget(QLabel('自定义命令:'))
        
        # 命令选择下拉框
        command_layout = QHBoxLayout()
        command_layout.addWidget(QLabel('命令:'))
        self.command_combo = QComboBox()
        self.command_combo.addItems(['前进', '后退', '停止'])
        command_layout.addWidget(self.command_combo)
        custom_layout.addLayout(command_layout)
        
        # 执行按钮
        self.execute_btn = QPushButton('执行命令')
        self.execute_btn.clicked.connect(self.execute_custom_command)
        custom_layout.addWidget(self.execute_btn)
        
        control_layout.addLayout(custom_layout, 3, 0, 1, 3)
        
        bottom_layout.addWidget(control_group)
        
        main_layout.addLayout(bottom_layout)
        
        # 设置中央部件
        self.setCentralWidget(central_widget)
        
        # 初始禁用控制按钮
        self.set_control_enabled(False)

    @pyqtSlot(int)
    def _start_stop_timer_slot(self, duration_ms):
        """在主线程中启动停止命令的定时器"""
        QTimer.singleShot(duration_ms, self._send_stop_command)
    
    def set_control_enabled(self, enabled):
        """启用或禁用控制按钮"""
        self.forward_btn.setEnabled(enabled)
        self.backward_btn.setEnabled(enabled)
        self.left_btn.setEnabled(enabled)
        self.right_btn.setEnabled(enabled)
        self.stop_btn.setEnabled(enabled)
        self.move_distance_btn.setEnabled(enabled)
        self.execute_btn.setEnabled(enabled)
        self.linear_speed_slider.setEnabled(enabled)
        self.angular_speed_slider.setEnabled(enabled)
        self.preset_combo.setEnabled(enabled)
    
    def update_duration(self, value):
        """更新录音时长"""
        self.record_duration = value
    
    def update_linear_speed(self, value):
        """更新线速度"""
        self.current_speed = value / 100.0
        self.linear_speed_label.setText(f'{self.current_speed:.2f} m/s')
    
    def update_angular_speed(self, value):
        """更新角速度"""
        angular_speed = value / 100.0
        self.angular_speed_label.setText(f'{angular_speed:.2f} rad/s')
    
    def update_distance(self, value):
        """更新移动距离"""
        self.current_distance = value / 100.0
        self.distance_label.setText(f'{self.current_distance:.2f} m')
        self.distance_spin.setValue(self.current_distance)
    
    def select_preset_speed(self, index):
        """选择预设速度"""
        preset_speeds = [0.3, 0.5, 0.8]  # 慢速, 中速, 快速
        if 0 <= index < len(preset_speeds):
            speed = preset_speeds[index]
            self.linear_speed_slider.setValue(int(speed * 100))
            self.current_speed = speed
    
    def connect_to_ros(self):
        """连接或断开ROS服务器"""
        if not self.ros_connected:
            self.update_ros_signal.emit("正在连接ROS...", "blue")
            threading.Thread(target=self._connect_ros_thread).start()
        else:
            # 断开连接
            self.ros_publisher.publish_cmd_vel()  # 发送停止命令
            self.ros_publisher.disconnect()
            self.ros_connected = False
            self.set_control_enabled(False)
            self.update_ros_signal.emit("未连接", "red")
    
    def _connect_ros_thread(self):
        """在后台线程中连接ROS"""
        try:
            if self.ros_publisher.connect():
                self.ros_connected = True
                self.update_ros_signal.emit("已连接", "green")
                self.set_control_enabled(True)
            else:
                self.update_ros_signal.emit("连接失败", "red")
        except Exception as e:
            self.update_ros_signal.emit(f"连接错误: {str(e)}", "red")
    
    def update_ros_bridge_url(self):
        """更新ROSBridge URL并重新连接"""
        new_url = self.ros_url_input.text().strip()
        if not new_url:
            self.update_status_signal.emit("ROSBridge URL不能为空", "red")
            return
            
        # 先断开现有连接
        if self.ros_connected:
            self.ros_publisher.publish_cmd_vel()  # 发送停止命令
            self.ros_publisher.disconnect()
            self.ros_connected = False
            
        # 更新URL
        self.ros_publisher.ros_bridge_url = new_url
        self.update_status_signal.emit(f"已更新ROSBridge URL: {new_url}", "blue")
        
        # 尝试连接
        self.connect_to_ros()
    
    def start_recognition(self):
        """开始语音识别"""
        if not self.ros_connected:
            self.update_status_signal.emit('请先连接ROS服务器', 'red')
            print_colored("请先连接ROS服务器", "red")
            return
        
        self.record_button.setEnabled(False)
        self.record_button.setText('录音中...')
        self.update_status_signal.emit(f'开始录音，持续{self.record_duration}秒...', 'blue')
        print_colored(f"开始录音，持续{self.record_duration}秒...", "blue")
        
        threading.Thread(target=self._recognition_thread).start()
    
    def _recognition_thread(self):
        """语音识别线程"""
        try:
            # 录制音频
            audio_file = self.recognizer.record_audio(duration=self.record_duration)
            self.update_status_signal.emit('录音完成，正在识别...', 'blue')
            print_colored("录音完成，正在识别...", "blue")
            
            # 语音识别
            text_result = self.recognizer.recognize_speech(audio_file)
            
            if not text_result:
                print_colored('未能识别到语音内容，请重试', 'red')
                self.update_status_signal.emit('未能识别到语音内容，请重试', 'red')
                self.update_recognition_signal.emit('未能识别到语音内容')
            else:
                print_colored(f'识别结果: {text_result}', 'green')
                self.update_status_signal.emit('语音识别成功', 'green')
                self.update_recognition_signal.emit(text_result)
                
                # 获取思考过程
                print_colored('正在思考...', 'blue')
                self.update_status_signal.emit('正在思考...', 'blue')
                thinking_process = self.decision_maker.think_aloud(text_result, play_thinking=False)
                self.update_thinking_signal.emit(thinking_process)
                
                # 进行决策分析
                print_colored('正在进行决策分析...', 'blue')
                self.update_status_signal.emit('正在进行决策分析...', 'blue')
                decision_result = self.decision_maker.make_decision(text_result, show_thinking=False)
                
                self.update_decision_signal.emit(json.dumps(decision_result, ensure_ascii=False, indent=2))
                
                # 处理决策结果
                self._process_decision_result(decision_result, text_result)
            
        except Exception as e:
            print_colored(f'语音识别过程中出错: {str(e)}', 'red')
            self.update_status_signal.emit(f'语音识别过程中出错: {str(e)}', 'red')
        
        finally:
            # 恢复按钮状态
            self.record_button.setEnabled(True)
            self.record_button.setText('开始对话')
    
    def _process_decision_result(self, decision_result, command_text=""):
        """处理决策结果"""
        if decision_result["status"] != "success":
            print_colored(f'决策分析出错: {decision_result.get("message", "未知错误")}', 'red')
            self.update_status_signal.emit(f'决策分析出错: {decision_result.get("message", "未知错误")}', 'red')
            return
            
        # 判断是否为顺序动作
        if decision_result.get("is_sequence", False) and decision_result.get("actions"):
            self._handle_sequence_actions(decision_result)
        else:
            self._handle_single_action(decision_result, command_text)
    
    def _handle_sequence_actions(self, decision_result):
        """处理顺序动作"""
        actions = decision_result.get("actions", [])
        if not actions:
            return
            
        message = decision_result.get("message", "执行顺序动作")
            
        self.update_status_signal.emit(f'顺序动作: {message}', 'green')
        self.update_ros_signal.emit(f"准备执行顺序动作: {message}", 'blue')
        
        # 显示动作序列详情
        for i, action in enumerate(actions):
            description = action.get("description", f"动作 {i+1}")
            duration = action.get("duration", 0)
            linear_x = action.get("linear_x", 0.0)
            angular_z = action.get("angular_z", 0.0)
            self.update_ros_signal.emit(
                f"  动作 {i+1}: {description} "
                f"(持续 {duration}s, lx={linear_x:.2f}, az={angular_z:.2f})", 
                'blue'
            )
        
        # 发送执行序列命令
        success, details = self.ros_publisher.process_decision(decision_result)
        
        if success:
            self.update_ros_signal.emit("顺序动作已开始执行", 'green')
            # 启动线程监控动作状态
            threading.Thread(target=self._update_action_status, args=(actions,)).start()
        else:
            self.update_ros_signal.emit(f"执行顺序动作失败: {details.get('error', '未知错误')}", 'red')
            
    def _update_action_status(self, actions):
        """更新动作执行状态"""
        try:
            for i, action in enumerate(actions):
                # 计算每个动作的大致执行时间（动作持续时间+停顿时间）
                duration = float(action.get("duration", 0.5)) + 0.5  # 动作时间+停顿时间
                # 等待动作执行完成
                time.sleep(duration)
                # 更新UI状态
                self.update_ros_signal.emit(f"动作 {i+1} 已完成: {action.get('description', f'动作 {i+1}')}", 'green')
            # 所有动作执行完毕
            self.update_ros_signal.emit(f"所有动作已执行完毕", 'green')
        except Exception as e:
            self.update_ros_signal.emit(f"动作状态更新出错: {str(e)}", 'red')
            
    def _handle_single_action(self, decision_result, command_text=""):
        """处理单个动作"""
        if "actions" in decision_result and isinstance(decision_result["actions"], list) and len(decision_result["actions"]) > 0:
            # 新格式
            action = decision_result["actions"][0]
            duration = action.get("duration", 0)
            linear_x = action.get("linear_x", 0.0)
            angular_z = action.get("angular_z", 0.0)
            description = action.get("description", "执行动作")
            message = decision_result.get("message", description)
        else:
            # 旧格式
            duration = decision_result.get("duration", 0)
            linear_x = decision_result.get("linear_x", 0.0)
            angular_z = decision_result.get("angular_z", 0.0)
            message = decision_result.get("message", f"执行: {command_text}")

        self.update_status_signal.emit(f'决策成功: {message}', 'green')
        self.update_ros_signal.emit(f"正在执行: {message} (持续 {duration}s)", 'blue')

        # 发布ROS速度命令
        self.ros_publisher.publish_cmd_vel(
            linear_x=linear_x,
            angular_z=angular_z
        )
        self.update_ros_signal.emit(f"速度命令已发送 (lx={linear_x:.2f}, az={angular_z:.2f})", 'green')

        # 如果有持续时间，则启动停止计时器
        if duration > 0:
            self.start_stop_timer_signal.emit(int(duration * 1000))
            self.update_ros_signal.emit(f"将在 {duration} 秒后自动停止", 'yellow')
        else:
            # 如果持续时间为0，通常是停止命令或瞬时动作，确保发送零速度
            QTimer.singleShot(100, self._send_stop_command)
    
    def manual_control(self, command):
        """手动控制机器人"""
        if not self.ros_connected:
            self.update_status_signal.emit('ROS未连接，无法发送命令', 'red')
            return
        
        try:
            vel = self.movement_controller.command_velocity_map.get(command)
            if vel:
                linear_x, angular_z = vel["linear"]["x"], vel["angular"]["z"]
                
                # 对于前进和后退，应用线速度
                if command in ["前进", "后退"] and linear_x != 0:
                    direction = 1 if linear_x > 0 else -1
                    linear_x = direction * self.current_speed * self.movement_controller.speed_scale
                # 对于旋转，应用角速度
                elif command in ["左转", "右转"] and angular_z != 0:
                    direction = 1 if angular_z > 0 else -1
                    angular_z = direction * (self.angular_speed_slider.value() / 100.0)
                
                self.update_ros_signal.emit(f"手动控制: {command} (lx={linear_x:.2f}, az={angular_z:.2f})", 'blue')
                
                # 发布ROS速度命令
                self.ros_publisher.publish_cmd_vel(
                    linear_x=linear_x,
                    angular_z=angular_z
                )
            else:
                self.update_ros_signal.emit(f"未知命令: {command}", 'red')
                
        except Exception as e:
            self.update_status_signal.emit(f'控制过程中出错: {str(e)}', 'red')
            print_colored(f"控制过程中出错: {str(e)}", "red")
    
    def move_with_distance(self):
        """按指定距离移动"""
        if not self.ros_connected:
            self.update_status_signal.emit('ROS未连接，无法发送命令', 'red')
            return
            
        distance = self.distance_spin.value()
        speed = self.current_speed
        
        # 计算移动时间（距离/速度）
        duration = distance / speed if speed > 0 else 0
        
        if duration <= 0:
            self.update_status_signal.emit('无效的距离或速度', 'red')
            return
            
        self.update_status_signal.emit(f'按距离移动: {distance}米, 速度: {speed}m/s, 持续: {duration:.1f}s', 'blue')
        self.update_ros_signal.emit(f"按距离移动: {distance}米 (速度: {speed}m/s, 持续: {duration:.1f}s)", 'blue')
        
        # 发送前进命令
        self.ros_publisher.publish_cmd_vel(linear_x=speed)
        
        # 设置定时器在指定时间后停止
        self.start_stop_timer_signal.emit(int(duration * 1000))
        self.update_ros_signal.emit(f"将在 {duration:.1f} 秒后自动停止", 'yellow')
    
    def execute_custom_command(self):
        """执行自定义命令"""
        command = self.command_combo.currentText()
        self.manual_control(command)
    
    @pyqtSlot(str, str)
    def update_status(self, message, color):
        """更新状态栏信息"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f'color: {color}; font-weight: bold;')
    
    @pyqtSlot(str)
    def update_recognition_text(self, text):
        """更新识别结果文本"""
        self.recognition_text.setText(text)
        self.recognition_text.moveCursor(QTextCursor.End)
    
    @pyqtSlot(str)
    def update_decision_text(self, text):
        """更新决策结果文本"""
        self.decision_text.setText(text)
        self.decision_text.moveCursor(QTextCursor.End)
    
    @pyqtSlot(str)
    def update_thinking_text(self, text):
        """更新思考过程文本"""
        self.thinking_text.setText(text)
        self.thinking_text.moveCursor(QTextCursor.End)
    
    @pyqtSlot(str, str)
    def update_ros_status(self, text, color):
        """更新ROS状态和日志"""
        if text == '已连接' or text == '连接失败' or text == '未连接' or text.startswith('连接错误'):
            # 更新连接状态
            self.ros_status.setText(text)
            self.ros_status.setStyleSheet(f'color: {color}; font-weight: bold;')
            
            # 更新连接按钮文本
            if text == '已连接':
                self.connect_button.setText('断开连接')
            else:
                self.connect_button.setText('连接ROS')
        else:
            # 添加到日志
            self.ros_log.append(text)
            cursor = self.ros_log.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.ros_log.setTextCursor(cursor)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.ros_connected:
            try:
                self.ros_publisher.publish_cmd_vel()
                self.ros_publisher.disconnect()
            except Exception as e:
                print(f"关闭连接时发生错误: {str(e)}")
        event.accept()

# 主函数
def run_integrated_interface(ros_bridge_url="ws://192.168.0.109:9090"):
    """运行集成Qt界面"""
    app = QApplication(sys.argv)
    window = IntegratedRobotControlUI(ros_bridge_url=ros_bridge_url)
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    run_integrated_interface()